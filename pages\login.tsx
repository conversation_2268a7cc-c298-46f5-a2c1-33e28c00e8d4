// Login Page with Redirect Logic (pages/login.tsx)
import { useQueryState } from 'blade/client/hooks';
import LoginForm from '../components/LoginForm.client';
import { PublicPageWrapper } from '../components/PublicPageWrapper.client';

const LoginPage = () => {
  const [roleParam] = useQueryState('role');
  const defaultRole = (roleParam as 'student' | 'teacher' | 'school_admin') || undefined;

  return (
    <PublicPageWrapper>
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ">
        <LoginForm defaultRole={defaultRole} />
      </div>
    </PublicPageWrapper>
  );
};

export default LoginPage;


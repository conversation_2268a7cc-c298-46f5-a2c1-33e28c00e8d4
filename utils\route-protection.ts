// utils/route-protection.ts
import React, { useEffect } from 'react';
import { useLocation, useRedirect } from 'blade/hooks';
import { useAuth } from '../hooks/useAuth';
import type { RouteConfig, UserRole } from '../lib/types';

// Define your route configuration
export const routeConfig: RouteConfig[] = [
  // Public routes (accessible to everyone)
  { path: '/', type: 'public' },
  { path: '/login', type: 'public' },
  { path: '/test-sidebar', type: 'public' },
  
  // Protected routes with role restrictions
  { path: '/student', type: 'protected', allowedRoles: ['student'] },
  { path: '/teacher', type: 'protected', allowedRoles: ['teacher'] },
  { path: '/school', type: 'protected', allowedRoles: ['school_admin'] },
];

/**
 * Hook to enforce route-based access control
 * This should be used in the root layout to protect all routes
 * Following Blade's philosophy: no loading states, instant redirects
 */
export const useRouteProtection = () => {
  const location = useLocation();
  const redirect = useRedirect();
  const { user, _getLoadingState } = useAuth();
  const loading = _getLoadingState();
  
  useEffect(() => {
    // In Blade, we avoid loading states - perform redirects immediately
    // Only skip if we're genuinely still determining auth state
    if (loading) return;
    
    const currentPath = location.pathname;
    const matchedRoute = findMatchingRoute(currentPath);
    
    if (!matchedRoute) {
      // No specific route config found, allow access
      return;
    }
    
    // Handle public routes (homepage, login)
    if (matchedRoute.type === 'public') {
      // Special handling for homepage and login - redirect authenticated users
      if (user && (currentPath === '/' || currentPath === '/login')) {
        // Check if this is a fresh login in progress (to avoid race condition with LoginForm redirect)
        const urlParams = new URLSearchParams(location.search);
        const isFreshLogin = urlParams.get('fresh_login') === 'true';

        // If it's a fresh login, let the LoginForm handle the redirect
        if (isFreshLogin) {
          console.log('Fresh login detected in route protection - skipping redirect to avoid race condition');
          return;
        }

        const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
        redirect(`/${rolePrefix}/${user.slug}`);
      }
      // Other public routes like /test-sidebar are accessible to everyone
      return;
    }
    
    // Handle protected routes
    if (matchedRoute.type === 'protected') {
      if (!user) {
        // Not authenticated - redirect to login with role hint if available
        const roleHint = matchedRoute.allowedRoles?.[0];
        const loginUrl = roleHint ? `/login?role=${roleHint}` : '/login';
        redirect(loginUrl);
        return;
      }
      
      // Check role authorization
      if (matchedRoute.allowedRoles && !matchedRoute.allowedRoles.includes(user.role)) {
        // Wrong role - redirect to user's correct area
        const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
        redirect(`/${rolePrefix}/${user.slug}`);
        return;
      }
    }
  }, [location.pathname, user, loading, redirect]);
  
  return { user, loading };
};

/**
 * Find the most specific route configuration for a given path
 */
function findMatchingRoute(path: string): RouteConfig | null {
  // Sort routes by specificity (longer paths first)
  const sortedRoutes = [...routeConfig].sort((a, b) => b.path.length - a.path.length);
  
  for (const route of sortedRoutes) {
    if (path.startsWith(route.path)) {
      return route;
    }
  }
  
  return null;
}

/**
 * Component wrapper for route protection
 * Following Blade's philosophy: instant redirects, no loading spinners
 * Use this to wrap specific components that need protection
 */
export const withRouteProtection = <P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles?: UserRole[]
) => {
  return function ProtectedComponent(props: P) {
    const { user, _getLoadingState } = useAuth();
    const loading = _getLoadingState();
    const redirect = useRedirect();
    
    // In Blade, we avoid loading states - redirect immediately when possible
    useEffect(() => {
      if (loading) return; // Only wait if genuinely loading
      
      // Check authentication
      if (!user) {
        const roleHint = allowedRoles?.[0];
        const loginUrl = roleHint ? `/login?role=${roleHint}` : '/login';
        redirect(loginUrl);
        return;
      }
      
      // Check role authorization
      if (allowedRoles && !allowedRoles.includes(user.role)) {
        const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
        redirect(`/${rolePrefix}/${user.slug}`);
        return;
      }
    }, [user, loading, redirect, allowedRoles]);
    
    // If we're still loading or about to redirect, return null (no flash)
    if (loading || !user || (allowedRoles && !allowedRoles.includes(user.role))) {
      return null;
    }
    
    return React.createElement(Component, props);
  };
};

/**
 * Hook for checking if current user can access a specific route
 */
export const useCanAccessRoute = (path: string): boolean => {
  const { user } = useAuth();
  const matchedRoute = findMatchingRoute(path);
  
  if (!matchedRoute) return true; // No restrictions
  
  if (matchedRoute.type === 'public') {
    return !user; // Public routes only for non-authenticated users
  }
  
  if (matchedRoute.type === 'protected') {
    if (!user) return false; // Must be authenticated
    
    if (matchedRoute.allowedRoles) {
      return matchedRoute.allowedRoles.includes(user.role);
    }
    
    return true; // Authenticated and no role restrictions
  }
  
  return true;
};

/**
 * Simple access control component that shows/hides content based on roles
 * Following Blade's philosophy: instant rendering, no loading states
 */
export const ProtectedContent: React.FC<{
  allowedRoles?: UserRole[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
}> = ({ allowedRoles, fallback = null, children }) => {
  const { user, canAccessRoles } = useAuth();
  
  if (!user) return fallback as React.ReactElement;
  
  if (allowedRoles && !canAccessRoles(allowedRoles)) {
    return fallback as React.ReactElement;
  }
  
  return children as React.ReactElement;
};
// components/PublicPageWrapper.client.tsx
'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useRedirect } from 'blade/hooks';

interface PublicPageWrapperProps {
  children: React.ReactNode;
  showLoadingSpinner?: boolean;
}

export const PublicPageWrapper: React.FC<PublicPageWrapperProps> = ({ 
  children, 
}) => {
  const { user, _getLoadingState } = useAuth();
  const loading = _getLoadingState();
  const redirect = useRedirect();
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    // If still loading auth state, don't render anything yet
    if (loading) {
      setShouldRender(false);
      return;
    }

    // If user is authenticated, redirect to their dashboard
    if (user) {
      console.log('Authenticated user detected on public page, redirecting to dashboard...');
      
      // Determine the correct dashboard based on user role
      const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
      const userSlug = user.slug || user.id;
      
      // Redirect immediately without rendering public content
      redirect(`/${rolePrefix}/${userSlug}`);
      return;
    }

    // User is not authenticated, safe to render public content
    setShouldRender(true);
  }, [user, loading, redirect]);

 

  // Only render public content if user is not authenticated
  return shouldRender ? <>{children}</> : null;
};

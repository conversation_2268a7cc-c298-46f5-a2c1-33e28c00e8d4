// components/LoginForm.client.tsx
import { useState, useRef } from 'react';
import { useRedirect } from 'blade/hooks';
import { useUnifiedSession } from '../lib/auth-client';
import type { UserRole } from '../lib/types';
import NoiseText from '../components/home/<USER>';
import FamilyStyleOTP from '../components/ui/family-style-otp.client';
import CustomWheelPicker, { type WheelPickerOption } from '../components/ui/custom-wheel-picker.client';
import { Badge } from '../components/ui/badge.client';
import { Link } from 'blade/client/components';
import {
  InputButton,
  InputButtonProvider,
  InputButtonAction,
  InputButtonSubmit,
  InputButtonInput,
} from '../components/ui/buttons/input';
import { Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'motion/react';

// Import the unified auth client
import { authClient, emailOtp } from '../lib/auth-client';

type AuthStep = 'role-selection' | 'credentials' | 'otp-verification';

interface LoginFormProps {
  defaultRole?: UserRole;
}

const LoginForm = ({ defaultRole }: LoginFormProps) => {
  const [selectedRole, setSelectedRole] = useState<UserRole>(defaultRole || 'teacher');
  const [currentStep, setCurrentStep] = useState<AuthStep>('role-selection');
  // Note: This loading state is for form submissions (user actions), not auth state loading
  // Form submission loading is acceptable in Blade as it represents active user interaction
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');
  const [showInput, setShowInput] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);

  // Form fields
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [otp, setOtp] = useState('');

  // Refs
  const inputRef = useRef<HTMLDivElement>(null);
  const messageTimeoutRef = useRef<NodeJS.Timeout>();

  const redirect = useRedirect();
  const { session } = useUnifiedSession();

  // Redirect if already authenticated
  if (session?.user) {
    const rolePrefix = session.user.role === 'school_admin' ? 'school' : session.user.role;
    redirect(`/${rolePrefix}/${session.user.slug}`);
    return null;
  }

  const resetForm = () => {
    // Clear any pending message timeouts
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }

    setEmail('');
    setUsername('');
    setPassword('');
    setOtp('');
    setError('');
    setMessage('');
    setMessageType('');
    setShowInput(false);
    setLoading(false);
  };

  const handleRoleSelection = (role: UserRole) => {
    setSelectedRole(role);
    setCurrentStep('credentials');
    resetForm();
  };

  const handleBack = () => {
    // Clear any pending message timeouts
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }

    if (currentStep === 'otp-verification') {
      setCurrentStep('credentials');
    } else if (currentStep === 'credentials') {
      setCurrentStep('role-selection');
    }
    setError('');
    setMessage('');
    setMessageType('');
  };

  // Student login (username only)
  const handleStudentLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim()) {
      setMessage('Username is required');
      setMessageType('error');

      // Clear validation error message after 3 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 3000);
      return;
    }

    setLoading(true);
    setMessage('');
    setMessageType('');

    try {
      const result = await authClient.signIn.username({
        username: username.trim(),
        password: password || 'student-default-password', // Students might not have passwords
      });

      if (result.error) {
        throw new Error(result.error.message || 'Login failed');
      }

      if (result.data?.user) {
        const userSlug = (result.data.user as any).slug || result.data.user.id;
        redirect(`/student/${userSlug}`);
      }
    } catch (error) {
      setMessage(error instanceof Error ? error.message : 'Login failed. Please check your username.');
      setMessageType('error');

      // Clear error message after 4 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    } finally {
      setLoading(false);
    }
  };

  // Teacher login (email/password or Google OAuth or OTP)
  const handleTeacherLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // For teacher login, show input first if not already shown
    if (!showInput) {
      setShowInput(true);
      return;
    }

    setLoading(true);
    setMessage('');
    setMessageType('');

    try {
      if (email && !password) {
        // Send OTP
        try {
          const result = await emailOtp.sendVerificationOtp({
            email: email.trim(),
            type: 'sign-in'
          });

          if (result.error) {
            // Handle structured error responses from server
            const errorData = result.error as any;

            // Better Auth wraps server errors, so we need to check multiple levels
            let actualError = errorData;

            // If the error has a 'body' property (from fetch response), parse it
            if (errorData.body && typeof errorData.body === 'string') {
              try {
                actualError = JSON.parse(errorData.body);
              } catch (e) {
                actualError = { message: errorData.body };
              }
            }

            // Check if we have a nested error object
            if (actualError.error && typeof actualError.error === 'object') {
              actualError = actualError.error;
            }

            // Now handle the error based on code or message
            if (actualError.code) {
              switch (actualError.code) {
                case 'WAITLIST_REQUIRED':
                  throw new Error('Your email is not on the waitlist. Please sign up first.');
                case 'WAITLIST_NOT_APPROVED':
                  throw new Error('Your account is awaiting approval. Please check back later.');
                case 'EMAIL_SERVICE_ERROR':
                  throw new Error('Email service is temporarily unavailable. Please try again later.');
                case 'WAITLIST_VALIDATION_ERROR':
                  throw new Error('Unable to verify waitlist status. Please try again later.');
                default:
                  throw new Error(actualError.message || 'Failed to send OTP');
              }
            } else {
              // Handle string error messages or generic objects
              const errorMessage = actualError.message || errorData.message || (typeof errorData === 'string' ? errorData : 'Failed to send OTP');
              throw new Error(errorMessage);
            }
          }

          setMessage('OTP sent to your email. Please check your inbox.');
          setMessageType('success');
          setCurrentStep('otp-verification');
        } catch (otpError) {
          // Handle specific OTP sending errors
          if (otpError instanceof Error) {
            if (otpError.message.includes('500') || otpError.message.includes('Internal Server Error')) {
              throw new Error('Email service is temporarily unavailable. Please try again later.');
            } else if (otpError.message.includes('WAITLIST_REQUIRED')) {
              throw new Error('Your email is not on the waitlist. Please sign up first.');
            } else if (otpError.message.includes('WAITLIST_NOT_APPROVED')) {
              throw new Error('Your account is awaiting approval. Please check back later.');
            } else {
              throw new Error(otpError.message || 'Failed to send OTP. Please try again.');
            }
          } else {
            throw new Error('Failed to send OTP. Please try again.');
          }
        }
      } else if (email && password) {
        // Email/password login
        const result = await authClient.signIn.email({
          email: email.trim(),
          password: password
        });

        if (result.error) {
          throw new Error(result.error.message || 'Invalid email or password');
        }

        if (result.data?.user) {
          const userSlug = (result.data.user as any).slug || result.data.user.id;
          redirect(`/teacher/${userSlug}`);
        }
      } else {
        setMessage('Please enter your email address');
        setMessageType('error');

        // Clear validation error message after 3 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 3000);
      }
    } catch (error) {
      // Enhanced error handling for server errors
      if (error instanceof Error) {
        if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
          setMessage('Server error occurred. Please try again later.');
          setMessageType('error');
        } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
          setMessage('Network error. Please check your connection and try again.');
          setMessageType('error');
        } else {
          setMessage(error.message);
          setMessageType('error');
        }
      } else {
        setMessage('Login failed. Please try again.');
        setMessageType('error');
      }

      // Clear error message after 4 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    } finally {
      setLoading(false);
    }
  };



  // School admin login (email OTP only)
  const handleSchoolLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // For school admin login, show input first if not already shown
    if (!showInput) {
      setShowInput(true);
      return;
    }

    setLoading(true);
    setMessage('');
    setMessageType('');

    try {
      if (!email.trim()) {
        setMessage('Email address is required');
        setMessageType('error');

        // Clear validation error message after 3 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 3000);
        return;
      }

      // Send OTP
      try {
        const result = await emailOtp.sendVerificationOtp({
          email: email.trim(),
          type: 'sign-in'
        });

        if (result.error) {
          // Handle structured error responses from server
          const errorData = result.error as any;

          // Better Auth wraps server errors, so we need to check multiple levels
          let actualError = errorData;

          // If the error has a 'body' property (from fetch response), parse it
          if (errorData.body && typeof errorData.body === 'string') {
            try {
              actualError = JSON.parse(errorData.body);
            } catch (e) {
              actualError = { message: errorData.body };
            }
          }

          // Check if we have a nested error object
          if (actualError.error && typeof actualError.error === 'object') {
            actualError = actualError.error;
          }

          // Now handle the error based on code or message
          if (actualError.code) {
            switch (actualError.code) {
              case 'WAITLIST_REQUIRED':
                throw new Error('Your email is not on the waitlist. Please sign up first.');
              case 'WAITLIST_NOT_APPROVED':
                throw new Error('Your account is awaiting approval. Please check back later.');
              case 'EMAIL_SERVICE_ERROR':
                throw new Error('Email service is temporarily unavailable. Please try again later.');
              case 'WAITLIST_VALIDATION_ERROR':
                throw new Error('Unable to verify waitlist status. Please try again later.');
              default:
                throw new Error(actualError.message || 'Failed to send OTP');
            }
          } else {
            // Handle string error messages or generic objects
            const errorMessage = actualError.message || errorData.message || (typeof errorData === 'string' ? errorData : 'Failed to send OTP');
            throw new Error(errorMessage);
          }
        }

        setMessage('OTP sent to your email. Please check your inbox.');
        setMessageType('success');
        setCurrentStep('otp-verification');
      } catch (otpError) {
        // Handle specific OTP sending errors for school admin
        if (otpError instanceof Error) {
          if (otpError.message.includes('500') || otpError.message.includes('Internal Server Error')) {
            throw new Error('Email service is temporarily unavailable. Please try again later.');
          } else if (otpError.message.includes('WAITLIST_REQUIRED')) {
            throw new Error('Your email is not on the waitlist. Please sign up first.');
          } else if (otpError.message.includes('WAITLIST_NOT_APPROVED')) {
            throw new Error('Your account is awaiting approval. Please check back later.');
          } else {
            throw new Error(otpError.message || 'Failed to send OTP. Please try again.');
          }
        } else {
          throw new Error('Failed to send OTP. Please try again.');
        }
      }
    } catch (error) {
      // Enhanced error handling for server errors
      if (error instanceof Error) {
        if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
          setMessage('Server error occurred. Please try again later.');
          setMessageType('error');
        } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
          setMessage('Network error. Please check your connection and try again.');
          setMessageType('error');
        } else {
          setMessage(error.message);
          setMessageType('error');
        }
      } else {
        setMessage('Failed to send OTP. Please try again.');
        setMessageType('error');
      }

      // Clear error message after 4 seconds
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
      messageTimeoutRef.current = setTimeout(() => {
        setMessage('');
        setMessageType('');
      }, 4000);
    } finally {
      setLoading(false);
    }
  };

  // OTP verification
  const handleOtpVerification = async (otpValue?: string) => {
    const otpToVerify = otpValue || otp;
    
    if (!otpToVerify.trim()) {
      setError('Please enter the OTP code');
      return;
    }

    setLoading(true);
    setError('');

    try {
      let result;
      
      result = await authClient.signIn.emailOtp({
        email: email.trim(),
        otp: otpToVerify.trim()
      });

      if (result?.error) {
        throw new Error(result.error.message || 'Invalid OTP code');
      }

      if (result?.data?.user) {
        const rolePrefix = selectedRole === 'school_admin' ? 'school' : selectedRole;
        const userSlug = (result.data.user as any).slug || result.data.user.id;
        redirect(`/${rolePrefix}/${userSlug}`);
      }
    } catch (error) {
      if (error instanceof Error && error.message === 'WAITLIST_NOT_APPROVED') {
        setError('Your account is awaiting approval. Please check back later.');
      } else if (error instanceof Error && error.message === 'WAITLIST_REQUIRED') {
        setError('Your email is not on the waitlist. Please sign up first.');
      } else {
        setError(error instanceof Error ? error.message : 'Invalid OTP code');
      }
      throw error; // Re-throw to let FamilyStyleOTP handle the error state
    } finally {
      setLoading(false);
    }
  };

  // Resend OTP function
  const handleResendOtp = async () => {
    if (selectedRole === 'teacher') {
      // Resend OTP for teacher
      setLoading(true);

      // Clear any pending message timeouts
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }

      setMessage('');
      setMessageType('');
      try {
        try {
          const result = await emailOtp.sendVerificationOtp({
            email: email.trim(),
            type: 'sign-in'
          });
          if (result.error) {
            const errorData = result.error as any;
            
            // Better Auth wraps server errors, so we need to check multiple levels
            let actualError = errorData;
            
            // If the error has a 'body' property (from fetch response), parse it
            if (errorData.body && typeof errorData.body === 'string') {
              try {
                actualError = JSON.parse(errorData.body);
              } catch (e) {
                actualError = { message: errorData.body };
              }
            }
            
            // Check if we have a nested error object
            if (actualError.error && typeof actualError.error === 'object') {
              actualError = actualError.error;
            }
            
            // Now handle the error based on code or message
            if (actualError.code) {
              switch (actualError.code) {
                case 'WAITLIST_REQUIRED':
                  throw new Error('Your email is not on the waitlist. Please sign up first.');
                case 'WAITLIST_NOT_APPROVED':
                  throw new Error('Your account is awaiting approval. Please check back later.');
                case 'EMAIL_SERVICE_ERROR':
                  throw new Error('Email service is temporarily unavailable. Please try again later.');
                case 'WAITLIST_VALIDATION_ERROR':
                  throw new Error('Unable to verify waitlist status. Please try again later.');
                default:
                  throw new Error(actualError.message || 'Failed to resend OTP');
              }
            } else {
              // Handle string error messages or generic objects
              const errorMessage = actualError.message || errorData.message || (typeof errorData === 'string' ? errorData : 'Failed to resend OTP');
              throw new Error(errorMessage);
            }
          }
          setMessage('OTP resent to your email.');
          setMessageType('success');

          // Clear success message after 3 seconds
          if (messageTimeoutRef.current) {
            clearTimeout(messageTimeoutRef.current);
          }
          messageTimeoutRef.current = setTimeout(() => {
            setMessage('');
            setMessageType('');
          }, 3000);
        } catch (otpError) {
          if (otpError instanceof Error) {
            if (otpError.message.includes('500') || otpError.message.includes('Internal Server Error')) {
              throw new Error('Email service is temporarily unavailable. Please try again later.');
            } else {
              throw new Error(otpError.message || 'Failed to resend OTP. Please try again.');
            }
          } else {
            throw new Error('Failed to resend OTP. Please try again.');
          }
        }
      } catch (error) {
        if (error instanceof Error) {
          if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
            setMessage('Server error occurred. Please try again later.');
            setMessageType('error');
          } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
            setMessage('Network error. Please check your connection and try again.');
            setMessageType('error');
          } else {
            setMessage(error.message);
            setMessageType('error');
          }
        } else {
          setMessage('Failed to resend OTP. Please try again.');
          setMessageType('error');
        }

        // Clear error message after 4 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 4000);
      } finally {
        setLoading(false);
      }
    } else if (selectedRole === 'school_admin') {
      // Resend OTP for school admin
      setLoading(true);

      // Clear any pending message timeouts
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }

      setMessage('');
      setMessageType('');
      try {
        try {
          const result = await emailOtp.sendVerificationOtp({
            email: email.trim(),
            type: 'sign-in'
          });
          if (result.error) {
            const errorData = result.error as any;
            
            // Better Auth wraps server errors, so we need to check multiple levels
            let actualError = errorData;
            
            // If the error has a 'body' property (from fetch response), parse it
            if (errorData.body && typeof errorData.body === 'string') {
              try {
                actualError = JSON.parse(errorData.body);
              } catch (e) {
                actualError = { message: errorData.body };
              }
            }
            
            // Check if we have a nested error object
            if (actualError.error && typeof actualError.error === 'object') {
              actualError = actualError.error;
            }
            
            // Now handle the error based on code or message
            if (actualError.code) {
              switch (actualError.code) {
                case 'WAITLIST_REQUIRED':
                  throw new Error('Your email is not on the waitlist. Please sign up first.');
                case 'WAITLIST_NOT_APPROVED':
                  throw new Error('Your account is awaiting approval. Please check back later.');
                case 'EMAIL_SERVICE_ERROR':
                  throw new Error('Email service is temporarily unavailable. Please try again later.');
                case 'WAITLIST_VALIDATION_ERROR':
                  throw new Error('Unable to verify waitlist status. Please try again later.');
                default:
                  throw new Error(actualError.message || 'Failed to resend OTP');
              }
            } else {
              // Handle string error messages or generic objects
              const errorMessage = actualError.message || errorData.message || (typeof errorData === 'string' ? errorData : 'Failed to resend OTP');
              throw new Error(errorMessage);
            }
          }
          setMessage('OTP resent to your email.');
          setMessageType('success');

          // Clear success message after 3 seconds
          if (messageTimeoutRef.current) {
            clearTimeout(messageTimeoutRef.current);
          }
          messageTimeoutRef.current = setTimeout(() => {
            setMessage('');
            setMessageType('');
          }, 3000);
        } catch (otpError) {
          if (otpError instanceof Error) {
            if (otpError.message.includes('500') || otpError.message.includes('Internal Server Error')) {
              throw new Error('Email service is temporarily unavailable. Please try again later.');
            } else {
              throw new Error(otpError.message || 'Failed to resend OTP. Please try again.');
            }
          } else {
            throw new Error('Failed to resend OTP. Please try again.');
          }
        }
      } catch (error) {
        if (error instanceof Error) {
          if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
            setMessage('Server error occurred. Please try again later.');
            setMessageType('error');
          } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
            setMessage('Network error. Please check your connection and try again.');
            setMessageType('error');
          } else {
            setMessage(error.message);
            setMessageType('error');
          }
        } else {
          setMessage('Failed to resend OTP. Please try again.');
          setMessageType('error');
        }

        // Clear error message after 4 seconds
        if (messageTimeoutRef.current) {
          clearTimeout(messageTimeoutRef.current);
        }
        messageTimeoutRef.current = setTimeout(() => {
          setMessage('');
          setMessageType('');
        }, 4000);
      } finally {
        setLoading(false);
      }
    }
  };

  // Mouse interaction handlers for visual effects
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!inputRef.current) return;
    const rect = inputRef.current.getBoundingClientRect();
    setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
  };

  const handleMouseEnter = () => setOpacity(1);
  const handleMouseLeave = () => setOpacity(0);

  // Create wheel picker options for role selection
  const roleOptions: WheelPickerOption[] = [
    { value: 'student', label: 'Student' },
    { value: 'teacher', label: 'Teacher' },
    { value: 'school_admin', label: 'School' },
  ];

  // Role selection step
  if (currentStep === 'role-selection') {
    return (
      <div className="w-full relative">
        
        {/* Fixed NoiseText Header */}
        <div className="flex-shrink-0 fixed top-[13.5vh] left-0 right-0 z-10">
          <div className="text-center">
          <div className="flex flex-col justify-center items-center text-center w-full mx-auto relative">
              <div className="mb-1">
              <Badge
                variant="outline"
                className="font-manrope_1 text-xs w-fit"
              >
                Choose role
              </Badge>
            </div>
            <NoiseText
              text="Sign in"
              className="font-manrope_1 text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
            />
          
          </div>
            <p className="font-manrope_1 text-sm md:text-lg text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              Select how you want to sign in
            </p>
          </div>
        </div>

        {/* Content below fixed header */}
        <div className="fixed top-[40vh] w-full left-0 right-0 md:max-w-[30vw] mx-auto px-4">
          <div className="relative w-full max-w-[400px] mx-auto space-y-8">
            <CustomWheelPicker
              options={roleOptions}
              value={selectedRole}
              onValueChange={(value) => setSelectedRole(value as UserRole)}
              className="w-full"
            />

            {/* Continue Button using InputButton style */}
            <div className="relative w-full max-w-[400px] mx-auto">
              <InputButtonProvider
                showInput={false}
                setShowInput={() => {}}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                className="relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                onMouseMove={handleMouseMove}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <InputButton>
                  <button
                    onClick={() => handleRoleSelection(selectedRole)}
                    className="w-full h-full flex items-center justify-center text-inherit font-medium font-manrope_1"
                  >
                    Continue as {selectedRole === 'school_admin' ? 'School' : selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)}
                  </button>

                  <div
                    ref={inputRef}
                    className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
                    style={{
                      opacity,
                      WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                      maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                    } as React.CSSProperties}
                  />
                </InputButton>
              </InputButtonProvider>
            </div>

            {/* Keyboard navigation hint */}
            <div className="text-center">
              <p className="text-xs hidden md:block text-black/70 dark:text-white/70 font-manrope_1">
                Use Tab, Shift+Tab, or ↑↓ arrow keys to navigate
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // OTP verification step
  if (currentStep === 'otp-verification') {
    return (
      <div className="w-full flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 fixed top-[13.5vh] left-0 right-0 z-10">
          <div className="text-center">
            <NoiseText    
              text="Enter Verification Code"          
              className="font-manrope_1 text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
            />
            <p className="font-manrope_1 text-md md:text-xl text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              <button
                onClick={handleBack}
              className="font-manrope_1 text-sm text-black/60 dark:text-white/60 underline"
              >
                ← Back
              </button>
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="fixed top-[40vh] w-full left-0 right-0 md:max-w-[30vw] mx-auto px-4">
          <div className="relative w-full max-w-[400px] mx-auto">
            {/* OTP Input Container with consistent styling */}
            <div className="relative group w-full items-center justify-center select-none  text-sm transition-all duration-200 overflow-hidden ">
              <FamilyStyleOTP
                value={otp}
                onChange={setOtp}
                onSubmit={handleOtpVerification}
                loading={loading}
                error={error}
                email={email}
                onResend={handleResendOtp}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Credentials step
  return (
    <div className="w-full  relative">
      {/* Fixed NoiseText Header */}
      <div className="fixed top-[13.5vh] left-0 right-0 z-10">
        <div className="text-center relative w-full">
          <div className="flex flex-col justify-center items-center text-center w-full mx-auto relative">
         <div className="mb-1">
            <Badge
              variant="outline"
              className="font-manrope_1 text-xs w-fit"
            >
              Sign in
            </Badge>
            </div>
          <NoiseText
            text={`${selectedRole === 'school_admin' ? 'School' : selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)}`}
            className="font-manrope_1 text-center text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
          />
            
          </div>
          <p className="font-manrope_1 text-md md:text-xl text-black/70 dark:text-white/70 mt-4 leading-relaxed">
            <button
              onClick={handleBack}
              className="font-manrope_1 text-sm text-black/60 dark:text-white/60 underline"
            >
              ← Change role
            </button>
          </p>
        </div>
      </div>

      {/* Content below fixed header */}
        <div className="fixed top-[40vh] w-full left-0 right-0 md:max-w-[30vw] mx-auto px-4">
        {/* Student Login Form */}
        {selectedRole === 'student' && (
          <div className="relative w-full max-w-[400px] mx-auto">
            <form onSubmit={handleStudentLogin} className="relative gap-4 justify-center flex flex-col">
                <p className="font-manrope_1 text-center text-sm md:text-lg text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              Sign in as a student
            </p>
              <InputButtonProvider
                showInput={showInput}
                setShowInput={setShowInput}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                className="relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                onMouseMove={handleMouseMove}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <InputButton>
                  <AnimatePresence>
                    {!showInput && (
                      <motion.div
                        key="action-text"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="w-full h-full"
                      >
                        <InputButtonAction>
                          <div className="flex items-center gap-2 font-manrope_1">
                            Enter your username
                          </div>
                        </InputButtonAction>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <InputButtonSubmit
                    onClick={() => {}}
                    type="submit"
                    disabled={loading || (!username.trim() && showInput)}
                    message={message}
                    messageType={messageType}
                    isSubmitting={loading}
                    success={false}
                    className="disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <motion.span
                        key="pending"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Loader2 className="animate-spin" />
                      </motion.span>
                    ) : (
                      'Sign in'
                    )}
                  </InputButtonSubmit>

                  {showInput && (
                    <div className="flex items-center w-full pl-0">
                      <InputButtonInput
                        type="text"
                        placeholder="Enter your username"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        disabled={loading}
                        required
                        autoFocus
                        autoComplete="username"
                      />
                    </div>
                  )}

                  <div
                    ref={inputRef}
                    className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
                    style={{
                      opacity,
                      WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                      maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                    } as React.CSSProperties}
                  />
                </InputButton>
              </InputButtonProvider>
            </form>
            {/* Reserved space for helper text to prevent layout shift */}
            <div className="mt-2 h-5 flex items-center justify-center">
              {showInput && (
                <p className="font-manrope_1 text-xs text-black/70 dark:text-white/70 text-center">
                  Your username was created by your teacher
                </p>
              )}
            </div>
          </div>
        )}

        {/* Teacher Login Form */}
        {selectedRole === 'teacher' && (
          <div className="relative w-full max-w-[400px] mx-auto">
            <form onSubmit={handleTeacherLogin} className="relative gap-4 justify-center flex flex-col ">
               <p className="font-manrope_1 text-center text-sm md:text-lg text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              Sign in as a teacher
            </p>
              <InputButtonProvider
                showInput={showInput}
                setShowInput={setShowInput}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                className="relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                onMouseMove={handleMouseMove}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <InputButton>
                  <AnimatePresence>
                    {!showInput && (
                      <motion.div
                        key="action-text"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="w-full h-full"
                      >
                        <InputButtonAction>
                          <div className="flex items-center gap-2 font-manrope_1">
                            Enter your email address
                          </div>
                        </InputButtonAction>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <InputButtonSubmit
                    onClick={() => {}}
                    type="submit"
                    disabled={loading || (!email.includes('@') && showInput)}
                    message={message}
                    messageType={messageType}
                    isSubmitting={loading}
                    success={messageType === 'success'}
                    className="disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <motion.span
                        key="pending"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Loader2 className="animate-spin" />
                      </motion.span>
                    ) : (
                      'Send Verification Code'
                    )}
                  </InputButtonSubmit>

                  {showInput && (
                    <div className="flex items-center w-full pl-0">
                      <InputButtonInput
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={loading}
                        required
                        autoFocus
                        autoComplete="email"
                      />
                    </div>
                  )}

                  <div
                    ref={inputRef}
                    className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
                    style={{
                      opacity,
                      WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                      maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                    } as React.CSSProperties}
                  />
                </InputButton>
              </InputButtonProvider>
            </form>
            {/* Reserved space for helper text to prevent layout shift */}
            <div className="mt-2 h-5 flex items-center justify-center">
              {showInput && (
                <p className="font-manrope_1 text-xs text-black/70 dark:text-white/70 text-center">
                  We'll send a verification code to your email
                </p>
              )}
            </div>
          </div>
        )}

        {/* School Admin Login Form */}
        {selectedRole === 'school_admin' && (
          <div className="relative w-full max-w-[400px] mx-auto">
            <form onSubmit={handleSchoolLogin} className="relative gap-4 justify-center flex flex-col">
               <p className="font-manrope_1 text-center text-sm md:text-lg text-black/70 dark:text-white/70 mt-4 leading-relaxed">
              Sign in as school admin
            </p>
              <InputButtonProvider
                showInput={showInput}
                setShowInput={setShowInput}
                transition={{ type: 'spring', stiffness: 400, damping: 30 }}
                className="relative group w-full items-center justify-center h-12 select-none rounded-full px-3 text-sm leading-8 transition-all duration-200 overflow-hidden bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]"
                onMouseMove={handleMouseMove}
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <InputButton>
                  <AnimatePresence>
                    {!showInput && (
                      <motion.div
                        key="action-text"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="w-full h-full"
                      >
                        <InputButtonAction>
                          <div className="flex items-center gap-2 font-manrope_1">
                            Enter your email address
                          </div>
                        </InputButtonAction>
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <InputButtonSubmit
                    onClick={() => {}}
                    type="submit"
                    disabled={loading || (!email.includes('@') && showInput)}
                    message={message}
                    messageType={messageType}
                    isSubmitting={loading}
                    success={messageType === 'success'}
                    className="disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <motion.span
                        key="pending"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Loader2 className="animate-spin" />
                      </motion.span>
                    ) : (
                      'Send Verification Code'
                    )}
                  </InputButtonSubmit>

                  {showInput && (
                    <div className="flex items-center w-full pl-0">
                      <InputButtonInput
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={loading}
                        required
                        autoFocus
                        autoComplete="email"
                      />
                    </div>
                  )}

                  <div
                    ref={inputRef}
                    className="pointer-events-none absolute inset-0 rounded-full border-2 border-orange-500/50 dark:border-orange-400/50 transition-opacity duration-500"
                    style={{
                      opacity,
                      WebkitMaskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                      maskImage: `radial-gradient(30% 30px at ${position.x}px ${position.y}px, black 45%, transparent)`,
                    } as React.CSSProperties}
                  />
                </InputButton>
              </InputButtonProvider>
            </form>
            {/* Reserved space for helper text to prevent layout shift */}
            <div className="mt-2 h-5 flex items-center justify-center">
              {showInput && (
                <p className="font-manrope_1 text-xs text-black/70 dark:text-white/70 text-center">
                  We'll send a verification code to your email
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginForm;
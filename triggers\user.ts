// triggers/user.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, SetTrigger, GetTrigger } from 'blade/types';

// Trigger for creating users
export const add: AddTrigger = (query) => {
  // Ensure required fields are set with proper defaults

  // Set default name if not provided (use email prefix)
  if (!query.with.name && query.with.email) {
    query.with.name = query.with.email.split('@')[0];
  } else if (!query.with.name) {
    query.with.name = 'User';
  }

  // Auto-generate slug if not provided
  if (!query.with.slug) {
    const baseName = query.with.name || (query.with.email ? query.with.email.split('@')[0] : 'user');
    query.with.slug = baseName
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '') || 'user';
  }

  // Set default emailVerified if not provided
  if (query.with.emailVerified === undefined) {
    query.with.emailVerified = false;
  }

  // Set default role if not provided (teacher for OTP users, student otherwise)
  if (!query.with.role) {
    // If this user creation is coming from OTP (has email but no explicit role), default to teacher
    query.with.role = query.with.email ? 'teacher' : 'student';
  }

  // Set default timestamps
  query.with.createdAt = new Date();
  query.with.updatedAt = new Date();

  return query;
};

// Trigger for updating users
export const set: SetTrigger = (query) => {
  // Update timestamp
  query.to.updatedAt = new Date();
  
  // Update slug if name is being changed
  if (query.to.name && !query.to.slug) {
    query.to.slug = query.to.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');
  }
  
  return query;
};

// Trigger for getting users (can be used for access control)
export const get: GetTrigger = (query) => {
  // Add any access control logic here if needed
  return query;
};
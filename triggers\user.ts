// triggers/user.ts
import type { <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON>rigger, GetTrigger } from 'blade/types';

// Trigger for creating users
export const add: AddTrigger = (query) => {
  // Ensure query.with exists
  if (!query.with) {
    return query;
  }

  // Handle both single object and array cases
  const processUserData = (userData: any) => {
    // Set default name if not provided (use email prefix)
    if (!userData.name && userData.email) {
      userData.name = userData.email.split('@')[0];
    } else if (!userData.name) {
      userData.name = 'User';
    }

    // Auto-generate slug if not provided
    if (!userData.slug) {
      const baseName = userData.name || (userData.email ? userData.email.split('@')[0] : 'user');
      userData.slug = baseName
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '') || 'user';
    }

    // Set default emailVerified if not provided
    if (userData.emailVerified === undefined) {
      userData.emailVerified = false;
    }

    // Set default role if not provided (teacher for OTP users, student otherwise)
    if (!userData.role) {
      // If this user creation is coming from OTP (has email but no explicit role), default to teacher
      userData.role = userData.email ? 'teacher' : 'student';
    }

    // Set default timestamps
    userData.createdAt = new Date();
    userData.updatedAt = new Date();

    return userData;
  };

  // Handle array of users
  if (Array.isArray(query.with)) {
    query.with = query.with.map(processUserData);
  } else {
    // Handle single user
    query.with = processUserData(query.with);
  }

  return query;
};

// Trigger for updating users
export const set: SetTrigger = (query) => {
  // Ensure query.to exists
  if (!query.to) {
    return query;
  }

  // Update timestamp
  (query.to as any)['updatedAt'] = new Date();

  // Update slug if name is being changed
  if ((query.to as any)['name'] && !(query.to as any)['slug']) {
    (query.to as any)['slug'] = (query.to as any)['name']
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '') || 'user';
  }

  return query;
};

// Trigger for getting users (can be used for access control)
export const get: GetTrigger = (query) => {
  // Add any access control logic here if needed
  return query;
};
//pages/index.tsx
import { useQueryState } from 'blade/client/hooks';
import HeroSection from '../components/home/<USER>';
import FeatureSection from '../components/home/<USER>';
import { PublicPageWrapper } from '../components/PublicPageWrapper.client';

const HomePage = () => {
  const [userType] = useQueryState('type');
  const currentType = (userType as 'student' | 'teacher' | 'school_admin') || 'student';
  const googleMapsApiKey = process.env["BLADE_GOOGLE_PLACES_API_KEY"] || '';

  return (
    <PublicPageWrapper>
      <div className="min-h-screen">
        <HeroSection userType={currentType} apiKey={googleMapsApiKey} />
        <FeatureSection userType={currentType} />
      </div>
    </PublicPageWrapper>
  );
};

export default HomePage;
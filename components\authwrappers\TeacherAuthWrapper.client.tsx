// components/authwrappers/TeacherAuthWrapper.client.tsx
'use client';

import { useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useRedirect, useLocation } from 'blade/hooks';

interface TeacherAuthWrapperProps {
  children: React.ReactNode;
}

export const TeacherAuthWrapper: React.FC<TeacherAuthWrapperProps> = ({ children }) => {
  const { user, _getLoadingState } = useAuth();
  const loading = _getLoadingState();
  const redirect = useRedirect();
  const location = useLocation();
  
  useEffect(() => {
    // Only redirect when we're sure about auth state
    if (loading) return;

    // Check if this is a fresh login with updated user data
    const urlParams = new URLSearchParams(location.search);
    const isFreshLogin = urlParams.get('fresh_login') === 'true';
    const urlRole = urlParams.get('role');

    // If it's a fresh login and the URL role is teacher, skip redirect checks
    if (isFreshLogin && urlRole === 'teacher') {
      console.log('Fresh login detected for teacher - skipping auth wrapper redirects');
      return;
    }

    // Redirect if not authenticated
    if (!user) {
      redirect('/login?role=teacher');
      return;
    }

    // Redirect if wrong role
    if (user.role !== 'teacher') {
      const rolePrefix = user.role === 'school_admin' ? 'school' : user.role;
      redirect(`/${rolePrefix}/${user.slug}`);
      return;
    }
  }, [user, loading, redirect, location]);
  
  // Don't render anything while loading or if user is null/wrong role
  if (loading || !user || user.role !== 'teacher') {
    return null;
  }
  
  return <>{children}</>;
};